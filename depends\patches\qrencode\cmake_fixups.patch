cmake: set minimum version to 3.5

Correct some dev warning output.

diff --git a/CMakeLists.txt b/CMakeLists.txt
index 773e037..a558145 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,4 +1,4 @@
-cmake_minimum_required(VERSION 3.1.0)
+cmake_minimum_required(VERSION 3.5)
 
 project(QRencode VERSION 4.1.1 LANGUAGES C)
 
@@ -20,7 +20,7 @@ list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake")
 set(CMAKE_THREAD_PREFER_PTHREAD ON)
 find_package(Threads)
 find_package(PNG)
-find_package(Iconv)
+find_package(ICONV)
 
 if(CMAKE_USE_PTHREADS_INIT)
     add_definitions(-DHAVE_LIBPTHREAD=1)
