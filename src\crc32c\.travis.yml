# Build matrix / environment variables are explained on:
# http://about.travis-ci.org/docs/user/build-configuration/
# This file can be validated on: http://lint.travis-ci.org/

language: cpp
dist: bionic
osx_image: xcode12.5

compiler:
- gcc
- clang
os:
- linux
- osx

env:
- GLOG=1 SHARED_LIB=0 BUILD_TYPE=Debug
- GLOG=1 SHARED_LIB=0 BUILD_TYPE=RelWithDebInfo
- GLOG=0 SHARED_LIB=0 BUILD_TYPE=Debug
- GLOG=0 SHARED_LIB=0 BUILD_TYPE=RelWithDebInfo
- GLOG=0 SHARED_LIB=1 BUILD_TYPE=Debug
- GLOG=0 SHARED_LIB=1 BUILD_TYPE=RelWithDebInfo

addons:
  apt:
    sources:
    - sourceline: 'deb http://apt.llvm.org/bionic/ llvm-toolchain-bionic-12 main'
      key_url: 'https://apt.llvm.org/llvm-snapshot.gpg.key'
    - sourceline: 'ppa:ubuntu-toolchain-r/test'
    packages:
    - clang-12
    - cmake
    - gcc-11
    - g++-11
    - ninja-build
  homebrew:
    packages:
    - cmake
    - gcc@11
    - llvm@12
    - ninja
    update: true

install:
# The following Homebrew packages aren't linked by default, and need to be
# prepended to the path explicitly.
- if [ "$TRAVIS_OS_NAME" = "osx" ]; then
    export PATH="$(brew --prefix llvm)/bin:$PATH";
  fi
# /usr/bin/gcc points to an older compiler on both Linux and macOS.
- if [ "$CXX" = "g++" ]; then export CXX="g++-11" CC="gcc-11"; fi
# /usr/bin/clang points to an older compiler on both Linux and macOS.
#
# Homebrew's llvm package doesn't ship a versioned clang++ binary, so the values
# below don't work on macOS. Fortunately, the path change above makes the
# default values (clang and clang++) resolve to the correct compiler on macOS.
- if [ "$TRAVIS_OS_NAME" = "linux" ]; then
    if [ "$CXX" = "clang++" ]; then export CXX="clang++-12" CC="clang-12"; fi;
  fi
- echo ${CC}
- echo ${CXX}
- ${CXX} --version
- cmake --version

before_script:
- mkdir -p build && cd build
- cmake .. -G Ninja -DCRC32C_USE_GLOG=$GLOG -DCMAKE_BUILD_TYPE=$BUILD_TYPE
           -DBUILD_SHARED_LIBS=$SHARED_LIB -DCMAKE_INSTALL_PREFIX=$HOME/.local
- cmake --build .
- cd ..

script:
- build/crc32c_tests
- build/crc32c_capi_tests
- build/crc32c_bench
- cd build && cmake --build . --target install
