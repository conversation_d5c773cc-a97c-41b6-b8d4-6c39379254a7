# /etc/conf.d/bitcoind: config file for /etc/init.d/bitcoind

# Config file location
#BITCOIND_CONFIGFILE="/etc/bitcoin/bitcoin.conf"

# What directory to write pidfile to?  (created and owned by $BITCOIND_USER)
#BITCOIND_PIDDIR="/var/run/bitcoind"

# What filename to give the pidfile
#BITCOIND_PIDFILE="${BITCOIND_PIDDIR}/bitcoind.pid"

# Where to write bitcoind data (be mindful that the blockchain is large)
#BITCOIND_DATADIR="/var/lib/bitcoind"

# User and group to own bitcoind process
#BITCOIND_USER="bitcoin"
#BITCOIND_GROUP="bitcoin"

# Path to bitcoind executable
#BITCOIND_BIN="/usr/bin/bitcoind"

# Nice value to run bitcoind under
#BITCOIND_NICE=0

# Additional options (avoid -conf and -datadir, use flags above)
#BITCOIND_OPTS=""

# The timeout in seconds OpenRC will wait for bitcoind to terminate
# after a SIGTERM has been raised.
# Note that this will be mapped as argument to start-stop-daemon's
# '--retry' option, which means you can specify a retry schedule
# here. For more information see man 8 start-stop-daemon.
BITCOIND_SIGTERM_TIMEOUT=600
