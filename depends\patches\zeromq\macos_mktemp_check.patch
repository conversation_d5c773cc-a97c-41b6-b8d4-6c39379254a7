build: fix mkdtemp check on macOS

On macOS, mkdtemp is in unistd.h. Fix the CMake check so that is works.
Upstreamed in https://github.com/zeromq/libzmq/pull/4668.

--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -599,7 +599,7 @@ if(NOT MSVC)
 
   check_cxx_symbol_exists(fork unistd.h HAVE_FORK)
   check_cxx_symbol_exists(gethrtime sys/time.h HAVE_GETHRTIME)
-  check_cxx_symbol_exists(mkdtemp stdlib.h HAVE_MKDTEMP)
+  check_cxx_symbol_exists(mkdtemp "stdlib.h;unistd.h" HAVE_MKDTEMP)
   check_cxx_symbol_exists(accept4 sys/socket.h HAVE_ACCEPT4)
   check_cxx_symbol_exists(strnlen string.h HAVE_STRNLEN)
 else()
