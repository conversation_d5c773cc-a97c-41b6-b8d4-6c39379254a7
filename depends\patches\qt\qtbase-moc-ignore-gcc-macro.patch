The moc executable loops through headers on CPLUS_INCLUDE_PATH and stumbles
on the GCC internal _GLIBCXX_VISIBILITY macro.  Tell it to ignore it as it is
not supposed to be looking there to begin with.

Upstream report: https://bugreports.qt.io/browse/QTBUG-83160

diff --git a/qtbase/src/tools/moc/main.cpp b/qtbase/src/tools/moc/main.cpp
--- a/qtbase/src/tools/moc/main.cpp
+++ b/qtbase/src/tools/moc/main.cpp
@@ -186,6 +186,7 @@ int runMoc(int argc, char **argv)
     dummyVariadicFunctionMacro.arguments += Symbol(0, PP_IDENTIFIER, "__VA_ARGS__");
     pp.macros["__attribute__"] = dummyVariadicFunctionMacro;
     pp.macros["__declspec"] = dummyVariadicFunctionMacro;
+    pp.macros["_GLIBCXX_VISIBILITY"] = dummyVariadicFunctionMacro;
 
     QString filename;
     QString output;
