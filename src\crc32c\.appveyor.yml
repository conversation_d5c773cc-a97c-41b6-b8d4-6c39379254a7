# Build matrix / environment variables are explained on:
# https://www.appveyor.com/docs/appveyor-yml/
# This file can be validated on: https://ci.appveyor.com/tools/validate-yaml

version: "{build}"

environment:
  matrix:
    # AppVeyor currently has no custom job name feature.
    # http://help.appveyor.com/discussions/questions/1623-can-i-provide-a-friendly-name-for-jobs
    - JOB: Visual Studio 2019
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
      CMAKE_GENERATOR: Visual Studio 16 2019

platform:
  - x86
  - x64

configuration:
  - RelWithDebInfo
  - Debug

build_script:
  - git submodule update --init --recursive
  - mkdir build
  - cd build
  - if "%platform%"=="x86" (set CMAKE_GENERATOR_PLATFORM="Win32")
      else (set CMAKE_GENERATOR_PLATFORM="%platform%")
  - cmake --version
  - cmake .. -G "%CMAKE_GENERATOR%" -A "%CMAKE_GENERATOR_PLATFORM%"
      -DCMAKE_CONFIGURATION_TYPES="%CONFIGURATION%" -DCRC32C_USE_GLOG=0
  - cmake --build . --config "%CONFIGURATION%"
  - cd ..

test_script:
  - build\%CONFIGURATION%\crc32c_tests.exe
  - build\%CONFIGURATION%\crc32c_capi_tests.exe
  - build\%CONFIGURATION%\crc32c_bench.exe
