description "Bitcoin Core Daemon"

start on runlevel [2345]
stop on starting rc RUNLEVEL=[016]

env BITCOIND_BIN="/usr/bin/bitcoind"
env BITCOIND_USER="bitcoin"
env BITCOIND_GROUP="bitcoin"
env BITCOIND_PIDDIR="/var/run/bitcoind"
# upstart can't handle variables constructed with other variables
env BITCOIND_PIDFILE="/var/run/bitcoind/bitcoind.pid"
env BITCOIND_CONFIGFILE="/etc/bitcoin/bitcoin.conf"
env BITCOIND_DATADIR="/var/lib/bitcoind"

expect fork

respawn
respawn limit 5 120
kill timeout 600

pre-start script
    # this will catch non-existent config files
    # bitcoind will check and exit with this very warning, but it can do so
    # long after forking, leaving upstart to think everything started fine.
    # since this is a commonly encountered case on install, just check and
    # warn here.
    if ! grep -qs '^rpcpassword=' "$BITCOIND_CONFIGFILE" ; then
        echo "ERROR: You must set a secure rpcpassword to run bitcoind."
        echo "The setting must appear in $BITCOIND_CONFIGFILE"
        echo
        echo "This password is security critical to securing wallets "
        echo "and must not be the same as the rpcuser setting."
        echo "You can generate a suitable random password using the following "
        echo "command from the shell:"
        echo
        echo "bash -c 'tr -dc a-zA-Z0-9 < /dev/urandom | head -c32 && echo'"
        echo
        echo "It is recommended that you also set alertnotify so you are "
        echo "notified of problems:"
        echo
        echo "ie: alertnotify=echo %%s | mail -s \"Bitcoin Alert\"" \
            "<EMAIL>"
        echo
        exit 1
    fi

    mkdir -p "$BITCOIND_PIDDIR"
    chmod 0755 "$BITCOIND_PIDDIR"
    chown $BITCOIND_USER:$BITCOIND_GROUP "$BITCOIND_PIDDIR"
    chown $BITCOIND_USER:$BITCOIND_GROUP "$BITCOIND_CONFIGFILE"
    chmod 0660 "$BITCOIND_CONFIGFILE"
end script

exec start-stop-daemon \
    --start \
    --pidfile "$BITCOIND_PIDFILE" \
    --chuid $BITCOIND_USER:$BITCOIND_GROUP \
    --exec "$BITCOIND_BIN" \
    -- \
    -pid="$BITCOIND_PIDFILE" \
    -conf="$BITCOIND_CONFIGFILE" \
    -datadir="$BITCOIND_DATADIR" \
    -disablewallet \
    -daemon

