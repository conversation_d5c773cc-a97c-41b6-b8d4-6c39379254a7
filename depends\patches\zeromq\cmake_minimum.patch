Set a more sane cmake_minimum_required.

--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -1,12 +1,7 @@
 # CMake build script for ZeroMQ
+cmake_minimum_required(VERSION 3.16)
 project(ZeroMQ)
 
-if(${CMAKE_SYSTEM_NAME} STREQUAL Darwin)
-  cmake_minimum_required(VERSION 3.0.2)
-else()
-  cmake_minimum_required(VERSION 2.8.12)
-endif()
-
 include(CheckIncludeFiles)
 include(CheckCCompilerFlag)
 include(CheckCXXCompilerFlag)
