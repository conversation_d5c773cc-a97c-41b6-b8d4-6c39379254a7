commit ff231d267370493814f933d151441866bf1e200b
Author: <PERSON> <<EMAIL>>
Date:   Fri Feb 23 13:21:08 2024 +0100

    Problem: cmake search for kqueue missing headers
    
    Solution: include sys/types.h and sys/time.h as documented by kqueue
    and used in autotools
    
    fixes kqueue detection on openbsd

diff --git a/CMakeLists.txt b/CMakeLists.txt
index f956f3fd..814d5d46 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -380,7 +380,7 @@ endif(WIN32)
 
 if(NOT MSVC)
   if(POLLER STREQUAL "")
-    check_cxx_symbol_exists(kqueue sys/event.h HAVE_KQUEUE)
+    check_cxx_symbol_exists(kqueue "sys/types.h;sys/event.h;sys/time.h" HAVE_KQUEUE)
     if(HAVE_KQUEUE)
       set(POLLER "kqueue")
     endif()
