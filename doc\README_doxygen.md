\mainpage notitle

\section intro_sec Introduction

This is the developer documentation of the reference client for an experimental new digital currency called Bitcoin,
which enables instant payments to anyone, anywhere in the world. Bitcoin uses peer-to-peer technology to operate
with no central authority: managing transactions and issuing money are carried out collectively by the network.

The software is a community-driven open source project, released under the MIT license.

See https://github.com/bitcoin/bitcoin and https://bitcoincore.org/ for further information about the project.

\section Navigation
Use <a href="modules.html"><code>Modules</code></a>, <a href="namespaces.html"><code>Namespaces</code></a>, <a href="classes.html"><code>Classes</code></a>, or <a href="files.html"><code>Files</code></a> at the top of the page to start navigating the code.

