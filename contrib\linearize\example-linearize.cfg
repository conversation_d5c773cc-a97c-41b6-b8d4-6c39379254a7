# bitcoind RPC settings (linearize-hashes)
rpcuser=someuser
rpcpassword=somepassword
#datadir=~/.bitcoin
host=127.0.0.1

#mainnet default
port=8332

#testnet default
#port=18332

#regtest default
#port=18443

#signet default
#port=38332

# bootstrap.dat hashlist settings (linearize-hashes)
max_height=313000

# bootstrap.dat input/output settings (linearize-data)

# mainnet
netmagic=f9beb4d9
genesis=000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f
input=/home/<USER>/.bitcoin/blocks

# testnet
#netmagic=0b110907
#genesis=000000000933ea01ad0ee984209779baaec3ced90fa3f408719526f8d77f4943
#input=/home/<USER>/.bitcoin/testnet3/blocks

# regtest
#netmagic=fabfb5da
#genesis=0f9188f13cb7b2c71f2a335e3a4fc328bf5beb436012afca590b1a11466e2206
#input=/home/<USER>/.bitcoin/regtest/blocks

# signet
#netmagic=0a03cf40
#genesis=00000008819873e925422c1ff0f99f7cc9bbb232af63a077a480a3633bee1ef6
#input=/home/<USER>/.bitcoin/signet/blocks

# "output" option causes blockchain files to be written to the given location,
# with "output_file" ignored. If not used, "output_file" is used instead.
# output=/home/<USER>/blockchain_directory
output_file=/home/<USER>/Downloads/bootstrap.dat
hashlist=hashlist.txt

# Maximum size in bytes of out-of-order blocks cache in memory
out_of_order_cache_sz = 100000000

# Do we want the reverse the hash bytes coming from getblockhash?
rev_hash_bytes = False

# On a new month, do we want to set the access and modify times of the new
# blockchain file?
file_timestamp = 0
# Do we want to split the blockchain files given a new month or specific height?
split_timestamp = 0

# Do we want debug printouts?
debug_output = False
