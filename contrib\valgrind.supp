# This valgrind suppressions file includes known Valgrind warnings in our
# dependencies that cannot be fixed in-tree.
#
# Example use:
# $ valgrind --suppressions=contrib/valgrind.supp build/bin/test_bitcoin
# $ valgrind --suppressions=contrib/valgrind.supp --leak-check=full \
#       --show-leak-kinds=all build/bin/test_bitcoin
#
# To create suppressions for found issues, use the --gen-suppressions=all option:
# $ valgrind --suppressions=contrib/valgrind.supp --leak-check=full \
#       --show-leak-kinds=all --gen-suppressions=all --show-reachable=yes \
#       --error-limit=no build/bin/test_bitcoin
#
# Note that suppressions may depend on OS and/or library versions.
# Tested on aarch64 and x86_64 with Ubuntu Noble system libs, using clang-16
# and GCC, without gui.
{
   Suppress uninitialized bytes warning in compat code
   Memcheck:Param
   ioctl(TCSET{S,SW,SF})
   fun:tcsetattr
}
{
   Suppress leaks on shutdown
   Memcheck:Leak
   ...
   fun:_Z8ShutdownR11NodeContext
}
{
   Suppress leveldb leak
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   ...
   fun:_ZN7leveldb6DBImpl14BackgroundCallEv
}
{
   Suppress leveldb leak
   Memcheck:Leak
   fun:_Znwm
   ...
   fun:GetCoin
}
{
   Suppress LogInstance still reachable memory warning
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:_Znwm
   fun:_Z11LogInstancev
}
{
   Suppress BCLog::Logger::StartLogging() still reachable memory warning
   Memcheck:Leak
   match-leak-kinds: reachable
   fun:malloc
   ...
   fun:_ZN5BCLog6Logger12StartLoggingEv
}
{
   Suppress https://bugs.kde.org/show_bug.cgi?id=472219 - fixed in Valgrind 3.22.
   Memcheck:Param
   ppoll(ufds.events)
   obj:/lib/ld-musl-aarch64.so.1
}
