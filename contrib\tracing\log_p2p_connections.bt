#!/usr/bin/env bpftrace

BEGIN
{
  printf("Logging opened, closed, misbehaving, and evicted P2P connections\n")
}

usdt:./build/bin/bitcoind:net:inbound_connection
{
  $id = (int64) arg0;
  $addr = str(arg1);
  $conn_type = str(arg2);
  $network = (int32) arg3;
  $existing = (uint64) arg4;
  printf("INBOUND conn from %s: id=%ld, type=%s, network=%d, total=%d\n", $addr, $id, $conn_type, $network, $existing);
}

usdt:./build/bin/bitcoind:net:outbound_connection
{
  $id = (int64) arg0;
  $addr = str(arg1);
  $conn_type = str(arg2);
  $network = (int32) arg3;
  $existing = (uint64) arg4;
  printf("OUTBOUND conn to %s: id=%ld, type=%s, network=%d, total=%d\n", $addr, $id, $conn_type, $network, $existing);
}

usdt:./build/bin/bitcoind:net:closed_connection
{
  $id = (int64) arg0;
  $addr = str(arg1);
  $conn_type = str(arg2);
  $network = (int32) arg3;
  printf("CLOSED conn to %s: id=%ld, type=%s, network=%d, established=%ld\n", $addr, $id, $conn_type, $network, arg4);
}

usdt:./build/bin/bitcoind:net:evicted_inbound_connection
{
  $id = (int64) arg0;
  $addr = str(arg1);
  $conn_type = str(arg2);
  $network = (int32) arg3;
  printf("EVICTED conn to %s: id=%ld, type=%s, network=%d, established=%ld\n", $addr, $id, $conn_type, $network, arg4);
}

usdt:./build/bin/bitcoind:net:misbehaving_connection
{
  $id = (int64) arg0;
  $message = str(arg1);
  printf("MISBEHAVING conn id=%ld, message='%s'\n", $id, $message);
}
