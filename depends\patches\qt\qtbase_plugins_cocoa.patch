CMake: Fix macOS -no-feature-sessionmanager build with <PERSON>Make < 3.25

Work around CMake issue 23464.


See: https://codereview.qt-project.org/c/qt/qtbase/+/634002


--- a/qtbase/src/plugins/platforms/cocoa/CMakeLists.txt
+++ b/qtbase/src/plugins/platforms/cocoa/CMakeLists.txt
@@ -102,3 +102,10 @@ qt_internal_extend_target(QCocoaIntegrationPlugin CONDITION QT_FEATURE_sessionma
     SOURCES
         qcocoasessionmanager.cpp qcocoasessionmanager.h
 )
+
+# Work around CMake issue 23464
+if(CMAKE_VERSION VERSION_LESS "3.25" AND NOT QT_FEATURE_sessionmanager)
+    set_target_properties(QCocoaIntegrationPlugin PROPERTIES
+        DISABLE_PRECOMPILE_HEADERS ON
+    )
+endif()
