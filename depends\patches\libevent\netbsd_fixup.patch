Improve portability on NetBSD

According to GCC documentation, "the various `-std` options disable
certain keywords".
This change adheres to GCC's recommendation by replacing the `typeof`
keyword with its alternative, `__typeof__`.

See https://github.com/libevent/libevent/commit/1759485e9a59147a47a674f5132fcfe764e7748c.


--- a/kqueue.c
+++ b/kqueue.c
@@ -52,8 +52,8 @@
  * intptr_t, whereas others define it as void*.  There doesn't seem to be an
  * easy way to tell them apart via autoconf, so we need to use OS macros. */
 #if defined(__NetBSD__)
-#define PTR_TO_UDATA(x) ((typeof(((struct kevent *)0)->udata))(x))
-#define INT_TO_UDATA(x) ((typeof(((struct kevent *)0)->udata))(intptr_t)(x))
+#define PTR_TO_UDATA(x) ((__typeof__(((struct kevent *)0)->udata))(x))
+#define INT_TO_UDATA(x) ((__typeof__(((struct kevent *)0)->udata))(intptr_t)(x))
 #elif defined(EVENT__HAVE_INTTYPES_H) && !defined(__OpenBSD__) && !defined(__FreeBSD__) && !defined(__darwin__) && !defined(__APPLE__) && !defined(__CloudABI__)
 #define PTR_TO_UDATA(x)	((intptr_t)(x))
 #define INT_TO_UDATA(x) ((intptr_t)(x))
