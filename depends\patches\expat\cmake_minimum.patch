build: set minimum required CMake to 3.16

--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -33,7 +33,7 @@
 # Unlike most of Expat,
 # this file is copyrighted under the BSD-license for buildsystem files of KDE.
 
-cmake_minimum_required(VERSION 3.1.3)
+cmake_minimum_required(VERSION 3.16)
 
 # This allows controlling documented build time switches
 # when Expat is pulled in using the add_subdirectory function, e.g.
