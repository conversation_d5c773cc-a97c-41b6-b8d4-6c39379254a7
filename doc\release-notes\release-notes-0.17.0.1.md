Bitcoin Core version 0.17.0.1 is now available from:

  <https://bitcoincore.org/bin/bitcoin-core-0.17.0.1/>

This release provides a minor bug fix for 0.17.0.

Please report bugs using the issue tracker at GitHub:

  <https://github.com/bitcoin/bitcoin/issues>

To receive security and update notifications, please subscribe to:

  <https://bitcoincore.org/en/list/announcements/join/>

Notable changes
===============

An issue was solved with OSX dmg generation, affecting macOS 10.12 to 10.14,
which could cause <PERSON><PERSON> to crash on install.

There are no significant changes for other operating systems.

0.17.0.1 change log
===================

### Build system
- #14416 `eb2cc84` Fix OSX dmg issue (10.12 to 10.14) (jonasschnelli)

### Documentation
- #14509 `1b5af2c` [0.17] doc: use SegWit in getblocktemplate example (Sjors)

Credits
=======

Thanks to everyone who directly contributed to this release:

- <PERSON>
- <PERSON>
- <PERSON>jo<PERSON> Provoost
- <PERSON><PERSON><PERSON><PERSON>

