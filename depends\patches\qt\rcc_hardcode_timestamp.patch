Hardcode last modified timestamp in Qt RCC

This change allows the already built qt package to be reused even with
the SOURCE_DATE_EPOCH variable set, e.g., for Guix builds.


--- old/qtbase/src/tools/rcc/rcc.cpp
+++ new/qtbase/src/tools/rcc/rcc.cpp
@@ -201,14 +201,7 @@ void RCCFileInfo::writeDataInfo(RCCResourceLibrary &lib)
 
     if (lib.formatVersion() >= 2) {
         // last modified time stamp
-        const QDateTime lastModified = m_fileInfo.lastModified(QTimeZone::UTC);
-        quint64 lastmod = quint64(lastModified.isValid() ? lastModified.toMSecsSinceEpoch() : 0);
-        static const quint64 sourceDate = 1000 * qgetenv("QT_RCC_SOURCE_DATE_OVERRIDE").toULongLong();
-        if (sourceDate != 0)
-            lastmod = sourceDate;
-        static const quint64 sourceDate2 = 1000 * qgetenv("SOURCE_DATE_EPOCH").toULongLong();
-        if (sourceDate2 != 0)
-            lastmod = sourceDate2;
+        quint64 lastmod = quint64(1);
         lib.writeNumber8(lastmod);
         if (text || pass1)
             lib.writeChar('\n');
