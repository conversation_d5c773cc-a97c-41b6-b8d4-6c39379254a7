// Copyright (c) 2023-present The Bitcoin Core developers
// Distributed under the MIT software license, see the accompanying
// file COPYING or https://opensource.org/license/mit/.

#ifndef BITCOIN_CONFIG_H
#define BITCOIN_CONFIG_H

/* Version Build */
#define CLIENT_VERSION_BUILD @CLIENT_VERSION_BUILD@

/* Version is release */
#define CLIENT_VERSION_IS_RELEASE @CLIENT_VERSION_IS_RELEASE@

/* Major version */
#define CLIENT_VERSION_MAJOR @CLIENT_VERSION_MAJOR@

/* Minor version */
#define CLIENT_VERSION_MINOR @CLIENT_VERSION_MINOR@

/* Copyright holder(s) before %s replacement */
#define COPYRIGHT_HOLDERS "@COPYRIGHT_HOLDERS@"

/* Copyright holder(s) */
#define COPYRIGHT_HOLDERS_FINAL "@COPYRIGHT_HOLDERS_FINAL@"

/* Replacement for %s in copyright holders string */
#define COPYRIGHT_HOLDERS_SUBSTITUTION "@CLIENT_NAME@"

/* Copyright year */
#define COPYRIGHT_YEAR @COPYRIGHT_YEAR@

/* Define if external signer support is enabled */
#cmakedefine ENABLE_EXTERNAL_SIGNER 1

/* Define to 1 to enable tracepoints for Userspace, Statically Defined Tracing
   */
#cmakedefine ENABLE_TRACING 1

/* Define to 1 to enable wallet functions. */
#cmakedefine ENABLE_WALLET 1

/* Define to 1 if you have the declaration of `fork', and to 0 if you don't.
   */
#cmakedefine01 HAVE_DECL_FORK

/* Define to 1 if '*ifaddrs' are available. */
#cmakedefine HAVE_IFADDRS 1

/* Define to 1 if you have the declaration of `pipe2', and to 0 if you don't.
   */
#cmakedefine01 HAVE_DECL_PIPE2

/* Define to 1 if you have the declaration of `setsid', and to 0 if you don't.
   */
#cmakedefine01 HAVE_DECL_SETSID

/* Define to 1 if fdatasync is available. */
#cmakedefine HAVE_FDATASYNC 1

/* Define this symbol if the BSD getentropy system call is available with
   sys/random.h */
#cmakedefine HAVE_GETENTROPY_RAND 1

/* Define this symbol if the Linux getrandom function call is available */
#cmakedefine HAVE_GETRANDOM 1

/* Define this symbol if you have malloc_info */
#cmakedefine HAVE_MALLOC_INFO 1

/* Define this symbol if you have mallopt with M_ARENA_MAX */
#cmakedefine HAVE_MALLOPT_ARENA_MAX 1

/* Define to 1 if O_CLOEXEC flag is available. */
#cmakedefine01 HAVE_O_CLOEXEC

/* Define this symbol if you have posix_fallocate */
#cmakedefine HAVE_POSIX_FALLOCATE 1

/* Define this symbol if platform supports unix domain sockets */
#cmakedefine HAVE_SOCKADDR_UN 1

/* Define this symbol to build code that uses getauxval */
#cmakedefine HAVE_STRONG_GETAUXVAL 1

/* Define this symbol if the BSD sysctl() is available */
#cmakedefine HAVE_SYSCTL 1

/* Define this symbol if the BSD sysctl(KERN_ARND) is available */
#cmakedefine HAVE_SYSCTL_ARND 1

/* Define to 1 if std::system or ::wsystem is available. */
#cmakedefine HAVE_SYSTEM 1

/* Define to the address where bug reports for this package should be sent. */
#define CLIENT_BUGREPORT "@CLIENT_BUGREPORT@"

/* Define to the full name of this package. */
#define CLIENT_NAME "@CLIENT_NAME@"

/* Define to the home page for this package. */
#define CLIENT_URL "@PROJECT_HOMEPAGE_URL@"

/* Define to the version of this package. */
#define CLIENT_VERSION_STRING "@CLIENT_VERSION_STRING@"

/* Define to 1 if strerror_r returns char *. */
#cmakedefine STRERROR_R_CHAR_P 1

/* Define if dbus support should be compiled in */
#cmakedefine USE_DBUS 1

/* Define if QR support should be compiled in */
#cmakedefine USE_QRCODE 1

#endif //BITCOIN_CONFIG_H
