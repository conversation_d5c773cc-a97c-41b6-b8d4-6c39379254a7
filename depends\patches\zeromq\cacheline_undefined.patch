Use proper STREQUAL instead of EQUAL to compare strings.txt

See: https://github.com/zeromq/libzmq/pull/4711.

--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -476,7 +476,7 @@ execute_process(
 if(CACHELINE_SIZE STREQUAL ""
    OR CACHELINE_SIZE EQUAL 0
    OR CACHELINE_SIZE EQUAL -1
-   OR CACHELINE_SIZE EQUAL "undefined")
+   OR CACHELINE_SIZE STREQUAL "undefined")
   set(ZMQ_CACHELINE_SIZE 64)
 else()
   set(ZMQ_CACHELINE_SIZE ${CACHELINE_SIZE})
