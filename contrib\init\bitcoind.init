#!/usr/bin/env bash
#
#  bitcoind The bitcoin core server.
#
#
# chkconfig: 345 80 20
# description: bitcoind
# processname: bitcoind
#

# Source function library.
. /etc/init.d/functions

# you can override defaults in /etc/sysconfig/bitcoind, see below
if [ -f /etc/sysconfig/bitcoind ]; then
        . /etc/sysconfig/bitcoind
fi

RETVAL=0

prog=bitcoind
# you can override the lockfile via BITCOIND_LOCKFILE in /etc/sysconfig/bitcoind
lockfile=${BITCOIND_LOCKFILE-/var/lock/subsys/bitcoind}

# bitcoind defaults to /usr/bin/bitcoind, override with BITCOIND_BIN
bitcoind=${BITCOIND_BIN-/usr/bin/bitcoind}

# bitcoind opts default to -disablewallet, override with BITCOIND_OPTS
bitcoind_opts=${BITCOIND_OPTS--disablewallet}

start() {
    echo -n $"Starting $prog: "
    daemon $DAEMONOPTS $bitcoind $bitcoind_opts
    RETVAL=$?
    echo
    [ $RETVAL -eq 0 ] && touch $lockfile
    return $RETVAL
}

stop() {
    echo -n $"Stopping $prog: "
    killproc $prog -t600
    RETVAL=$?
    echo
    [ $RETVAL -eq 0 ] && rm -f $lockfile
    return $RETVAL
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    status)
        status $prog
        ;;
    restart)
        stop
        start
        ;;
    *)
        echo "Usage: service $prog {start|stop|status|restart}"
        exit 1
        ;;
esac
