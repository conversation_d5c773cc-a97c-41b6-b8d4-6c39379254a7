26.0 Release Notes
==================

Bitcoin Core version 26.0 is now available from:

  <https://bitcoincore.org/bin/bitcoin-core-26.0/>

This release includes new features, various bug fixes and performance
improvements, as well as updated translations.

Please report bugs using the issue tracker at GitHub:

  <https://github.com/bitcoin/bitcoin/issues>

To receive security and update notifications, please subscribe to:

  <https://bitcoincore.org/en/list/announcements/join/>

How to Upgrade
==============

If you are running an older version, shut it down. Wait until it has completely
shut down (which might take a few minutes in some cases), then run the
installer (on Windows) or just copy over `/Applications/Bitcoin-Qt` (on macOS)
or `bitcoind`/`bitcoin-qt` (on Linux).

Upgrading directly from a version of Bitcoin Core that has reached its EOL is
possible, but it might take some time if the data directory needs to be migrated. Old
wallet versions of Bitcoin Core are generally supported.

Compatibility
==============

Bitcoin Core is supported and extensively tested on operating systems
using the Linux kernel, macOS 11.0+, and Windows 7 and newer.  Bitcoin
Core should also work on most other Unix-like systems but is not as
frequently tested on them.  It is not recommended to use Bitcoin Core on
unsupported systems.

Notable changes
===============

P2P and network changes
-----------------------

- Experimental support for the v2 transport protocol defined in
  [BIP324](https://github.com/bitcoin/bips/blob/master/bip-0324.mediawiki) was added.
  It is off by default, but when enabled using `-v2transport` it will be negotiated
  on a per-connection basis with other peers that support it too. The existing
  v1 transport protocol remains fully supported.

- Nodes with multiple reachable networks will actively try to have at least one
  outbound connection to each network. This improves individual resistance to
  eclipse attacks and network level resistance to partition attacks. Users no
  longer need to perform active measures to ensure being connected to multiple
  enabled networks. (#27213)

Pruning
-------

- When using assumeutxo with `-prune`, the prune budget may be exceeded if it is set
  lower than 1100MB (i.e. `MIN_DISK_SPACE_FOR_BLOCK_FILES * 2`). Prune budget is normally
  split evenly across each chainstate, unless the resulting prune budget per chainstate
  is beneath `MIN_DISK_SPACE_FOR_BLOCK_FILES` in which case that value will be used. (#27596)

Updated RPCs
------------

- Setting `-rpcserialversion=0` is deprecated and will be removed in
  a future release. It can currently still be used by also adding
  the `-deprecatedrpc=serialversion` option. (#28448)

- The `hash_serialized_2` value has been removed from `gettxoutsetinfo` since the value it
  calculated contained a bug and did not take all data into account. It is superseded by
  `hash_serialized_3` which provides the same functionality but serves the correctly calculated hash. (#28685)

- New fields `transport_protocol_type` and `session_id` were added to the `getpeerinfo` RPC to indicate
  whether the v2 transport protocol is in use, and if so, what the session id is.

- A new argument `v2transport` was added to the `addnode` RPC to indicate whether a v2 transaction connection
  is to be attempted with the peer.

- [Miniscript](https://bitcoin.sipa.be/miniscript/) expressions can now be used in Taproot descriptors for all RPCs working with descriptors. (#27255)

- `finalizepsbt` is now able to finalize a PSBT with inputs spending [Miniscript](https://bitcoin.sipa.be/miniscript/)-compatible Taproot leaves. (#27255)

Changes to wallet related RPCs can be found in the Wallet section below.

New RPCs
--------

- `loadtxoutset` has been added, which allows loading a UTXO snapshot of the format
  generated by `dumptxoutset`. Once this snapshot is loaded, its contents will be
  deserialized into a second chainstate data structure, which is then used to sync to
  the network's tip.

  Meanwhile, the original chainstate will complete the initial block download process in
  the background, eventually validating up to the block that the snapshot is based upon.

  The result is a usable bitcoind instance that is current with the network tip in a
  matter of minutes rather than hours. UTXO snapshot are typically obtained via
  third-party sources (HTTP, torrent, etc.) which is reasonable since their contents
  are always checked by hash.

  You can find more information on this process in the `assumeutxo` design
  document (<https://github.com/bitcoin/bitcoin/blob/master/doc/design/assumeutxo.md>).

  `getchainstates` has been added to aid in monitoring the assumeutxo sync process.

- A new `getprioritisedtransactions` RPC has been added. It returns a map of all fee deltas created by the
  user with prioritisetransaction, indexed by txid. The map also indicates whether each transaction is
  present in the mempool. (#27501)

- A new RPC, `submitpackage`, has been added. It can be used to submit a list of raw hex
transactions to the mempool to be evaluated as a package using consensus and mempool policy rules.
These policies include package CPFP, allowing a child with high fees to bump a parent below the
mempool minimum feerate (but not minimum relay feerate). (#27609)

  - Warning: successful submission does not mean the transactions will propagate throughout the
    network, as package relay is not supported.

  - Not all features are available. The package is limited to a child with all of its
    unconfirmed parents, and no parent may spend the output of another parent.  Also, package
    RBF is not supported. Refer to doc/policy/packages.md for more details on package policies
    and limitations.

  - This RPC is experimental. Its interface may change.

- A new RPC `getaddrmaninfo` has been added to view the distribution of addresses in the new and tried table of the
  node's address manager across different networks(ipv4, ipv6, onion, i2p, cjdns). The RPC returns count of addresses
  in new and tried table as well as their sum for all networks. (#27511)

- A new `importmempool` RPC has been added. It loads a valid `mempool.dat` file and attempts to
  add its contents to the mempool. This can be useful to import mempool data from another node
  without having to modify the datadir contents and without having to restart the node. (#27460)
    - Warning: Importing untrusted files is dangerous, especially if metadata from the file is taken over.
    - If you want to apply fee deltas, it is recommended to use the `getprioritisedtransactions` and
      `prioritisetransaction` RPCs instead of the `apply_fee_delta_priority` option to avoid
      double-prioritising any already-prioritised transactions in the mempool.

Updated settings
----------------

- `bitcoind` and `bitcoin-qt` will now raise an error on startup
 if a datadir that is being used contains a bitcoin.conf file that
 will be ignored, which can happen when a datadir= line is used in
 a bitcoin.conf file. The error message is just a diagnostic intended
 to prevent accidental misconfiguration, and it can be disabled to
 restore the previous behavior of using the datadir while ignoring
 the bitcoin.conf contained in it. (#27302)

- Passing an invalid `-debug`, `-debugexclude`, or `-loglevel` logging configuration
  option now raises an error, rather than logging an easily missed warning. (#27632)

Changes to GUI or wallet related settings can be found in the GUI or Wallet section below.

New settings
------------

Tools and Utilities
-------------------

- A new `bitcoinconsensus_verify_script_with_spent_outputs` function is available in libconsensus which optionally accepts the spent outputs of the transaction being verified.
- A new `bitcoinconsensus_SCRIPT_FLAGS_VERIFY_TAPROOT` flag is available in libconsensus that will verify scripts with the Taproot spending rules.

Wallet
------

- Wallet loading has changed in this release. Wallets with some corrupted records that could be
  previously loaded (with warnings) may no longer load. For example, wallets with corrupted
  address book entries may no longer load. If this happens, it is recommended
  load the wallet in a previous version of Bitcoin Core and import the data into a new wallet.
  Please also report an issue to help improve the software and make wallet loading more robust
  in these cases. (#24914)

- The `createwallet` RPC will no longer create legacy (BDB) wallets when
  setting `descriptors=false` without also providing the
  `-deprecatedrpc=create_bdb` option. This is because the legacy wallet is
  being deprecated in a future release. (#28597)

- The `gettransaction`, `listtransactions`, `listsinceblock` RPCs now return
  the `abandoned` field for all transactions. Previously, the "abandoned" field
  was only returned for sent transactions. (#25158)

- The `listdescriptors`, `decodepsbt` and similar RPC methods now show `h` rather than apostrophe (`'`) to indicate
  hardened derivation. This does not apply when using the `private` parameter, which
  matches the marker used when descriptor was generated or imported. Newly created
  wallets use `h`. This change makes it easier to handle descriptor strings manually.
  E.g. the `importdescriptors` RPC call is easiest to use `h` as the marker: `'["desc": ".../0h/..."]'`.
  With this change `listdescriptors` will use `h`, so you can copy-paste the result,
  without having to add escape characters or switch `'` to 'h' manually.
  Note that this changes the descriptor checksum.
  For legacy wallets the `hdkeypath` field in `getaddressinfo` is unchanged,
  nor is the serialization format of wallet dumps. (#26076)

- The `getbalances` RPC now returns a `lastprocessedblock` JSON object which contains the wallet's last processed block
  hash and height at the time the balances were calculated. This result shouldn't be cached because importing new keys could invalidate it. (#26094)

- The `gettransaction` RPC now returns a `lastprocessedblock` JSON object which contains the wallet's last processed block
  hash and height at the time the transaction information was generated. (#26094)

- The `getwalletinfo` RPC now returns a `lastprocessedblock` JSON object which contains the wallet's last processed block
  hash and height at the time the wallet information was generated. (#26094)

- Coin selection and transaction building now accounts for unconfirmed low-feerate ancestor transactions. When it is necessary to spend unconfirmed outputs, the wallet will add fees to ensure that the new transaction with its ancestors will achieve a mining score equal to the feerate requested by the user. (#26152)

- For RPC methods which accept `options` parameters ((`importmulti`, `listunspent`,
  `fundrawtransaction`, `bumpfee`, `send`, `sendall`, `walletcreatefundedpsbt`,
  `simulaterawtransaction`), it is now possible to pass the options as named
  parameters without the need for a nested object. (#26485)

This means it is possible make calls like:

```sh
src/bitcoin-cli -named bumpfee txid fee_rate=100
```

instead of

```sh
src/bitcoin-cli -named bumpfee txid options='{"fee_rate": 100}'
```

- The `deprecatedrpc=walletwarningfield` configuration option has been removed.
  The `createwallet`, `loadwallet`, `restorewallet` and `unloadwallet` RPCs no
  longer return the "warning" string field. The same information is provided
  through the "warnings" field added in v25.0, which returns a JSON array of
  strings. The "warning" string field was deprecated also in v25.0. (#27757)

- The `signrawtransactionwithkey`, `signrawtransactionwithwallet`,
  `walletprocesspsbt` and `descriptorprocesspsbt` calls now return the more
  specific RPC_INVALID_PARAMETER error instead of RPC_MISC_ERROR if their
  sighashtype argument is malformed. (#28113)

- RPC `walletprocesspsbt`, and `descriptorprocesspsbt` return
  object now includes field `hex` (if the transaction
  is complete) containing the serialized transaction
  suitable for RPC `sendrawtransaction`. (#28414)

- It's now possible to use [Miniscript](https://bitcoin.sipa.be/miniscript/) inside Taproot leaves for descriptor wallets. (#27255)

Descriptors
-----------

- The usage of hybrid public keys in output descriptors has been removed. Hybrid
  public keys are an exotic public key encoding not supported by output descriptors
  (as specified in BIP380 and documented in doc/descriptors.md). Bitcoin Core would
  previously incorrectly accept descriptors containing such hybrid keys. (#28587)

GUI changes
-----------

- The transaction list in the GUI no longer provides a special category for "payment to yourself". Now transactions that have both inputs and outputs that affect the wallet are displayed on separate lines for spending and receiving. (gui#119)

- A new menu option allows migrating a legacy wallet based on keys and implied output script types stored in BerkeleyDB (BDB) to a modern wallet that uses descriptors stored in SQLite. (gui#738)

- The PSBT operations dialog marks outputs paying your own wallet with "own address". (gui#740)

- The ability to create legacy wallets is being removed. (gui#764)

Contrib
-------

- Bash completion files have been renamed from `bitcoin*.bash-completion` to
  `bitcoin*.bash`. This means completions can be automatically loaded on demand
  based on invoked commands' names when they are put into the completion
  directory (found with `pkg-config --variable=completionsdir
  bash-completion`) without requiring renaming. (#28507)

Low-level changes
=================

Tests
-----

- Non-standard transactions are now disabled by default on testnet
  for relay and mempool acceptance. The previous behaviour can be
  re-enabled by setting `-acceptnonstdtxn=1`. (#28354)

Credits
=======

Thanks to everyone who directly contributed to this release:

- 0xb10c
- Amiti Uttarwar
- Andrew Chow
- Andrew Toth
- Anthony Towns
- Antoine Poinsot
- Antoine Riard
- Ari
- Aurèle Oulès
- Ayush Singh
- Ben Woosley
- Brandon Odiwuor
- Brotcrunsher
- brunoerg
- Bufo
- Carl Dong
- Casey Carter
- Cory Fields
- David Álvarez Rosa
- dergoegge
- dhruv
- dimitaracev
- Erik Arvstedt
- Erik McKelvey
- Fabian Jahr
- furszy
- glozow
- Greg Sanders
- Harris
- Hennadii Stepanov
- Hernan Marino
- ishaanam
- ismaelsadeeq
- Jake Rawsthorne
- James O'Beirne
- John Moffett
- Jon Atack
- josibake
- kevkevin
- Kiminuo
- Larry Ruane
- Luke Dashjr
- MarcoFalke
- Marnix
- Martin Leitner-Ankerl
- Martin Zumsande
- Matthew Zipkin
- Michael Ford
- Michael Tidwell
- mruddy
- Murch
- ns-xvrn
- pablomartin4btc
- Pieter Wuille
- Reese Russell
- Rhythm Garg
- Ryan Ofsky
- Sebastian Falbesoner
- Sjors Provoost
- stickies-v
- stratospher
- Suhas Daftuar
- TheCharlatan
- Tim Neubauer
- Tim Ruffing
- Vasil Dimov
- virtu
- vuittont60
- willcl-ark
- Yusuf Sahin HAMZA

As well as to everyone that helped with translations on
[Transifex](https://www.transifex.com/bitcoin/bitcoin/).
