name: Good First Issue
description: (Regular devs only) Suggest a new good first issue
labels: [good first issue]
body:
  - type: markdown
    attributes:
      value: |
        Please add the label "good first issue" manually before or after opening

        A good first issue is an uncontroversial issue, that has a relatively unique and obvious solution

        Motivate the issue and explain the solution briefly
  - type: textarea
    id: motivation
    attributes:
      label: Motivation
      description: Motivate the issue
    validations:
      required: true
  - type: textarea
    id: solution
    attributes:
      label: Possible solution
      description: Describe a possible solution
    validations:
      required: false
  - type: textarea
    id: useful-skills
    attributes:
      label: Useful Skills
      description: For example, “`std::thread`”, “Qt6 GUI and async GUI design” or “basic understanding of Bitcoin mining and the Bitcoin Core RPC interface”.
      value: |
        * Compiling Bitcoin Core from source
        * Running the C++ unit tests and the Python functional tests
        * ...
  - type: textarea
    attributes:
      label: Guidance for new contributors
      description: Please leave this to automatically add the footer for new contributors
      value: |
        Want to work on this issue?

        For guidance on contributing, please read [CONTRIBUTING.md](https://github.com/bitcoin/bitcoin/blob/master/CONTRIBUTING.md) before opening your pull request.

