Avoid using native float16 types in Qt's qfloat16

Using native float16 types may introduce compatibility issues
in release binaries for Linux platforms.

See: https://gcc.gnu.org/git/?p=gcc.git;a=commit;h=e42d2d2a20f2bb59928bc895ec9f46503a1b5c73

This patch can be dropped once the minimum required libgcc version
is updated to 12.x or newer.


--- a/qtbase/src/corelib/global/qtypes.h
+++ b/qtbase/src/corelib/global/qtypes.h
@@ -258,23 +258,8 @@ using qsizetype = QIntegerForSizeof<std::size_t>::Signed;
 
 // Define a native float16 type
 namespace QtPrivate {
-#if defined(__STDCPP_FLOAT16_T__)
-#  define QFLOAT16_IS_NATIVE        1
-using NativeFloat16Type = std::float16_t;
-#elif defined(Q_CC_CLANG) && defined(__FLT16_MAX__) && 0
-// disabled due to https://github.com/llvm/llvm-project/issues/56963
-#  define QFLOAT16_IS_NATIVE        1
-using NativeFloat16Type = decltype(__FLT16_MAX__);
-#elif defined(Q_CC_GNU_ONLY) && defined(__FLT16_MAX__) && defined(__ARM_FP16_FORMAT_IEEE)
-#  define QFLOAT16_IS_NATIVE        1
-using NativeFloat16Type = __fp16;
-#elif defined(Q_CC_GNU_ONLY) && defined(__FLT16_MAX__) && defined(__SSE2__)
-#  define QFLOAT16_IS_NATIVE        1
-using NativeFloat16Type = _Float16;
-#else
 #  define QFLOAT16_IS_NATIVE        0
 using NativeFloat16Type = void;
-#endif
 } // QtPrivate
 
 #endif // __cplusplus
