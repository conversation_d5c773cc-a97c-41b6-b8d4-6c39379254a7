# Copyright (c) 2023-present The Bitcoin Core developers
# Distributed under the MIT software license, see the accompanying
# file COPYING or https://opensource.org/license/mit/.

# This file is expected to be highly volatile and may still change substantially.

# If CMAKE_SYSTEM_NAME is set within a toolchain file, <PERSON><PERSON><PERSON> will also
# set CMAKE_CROSSCOMPILING to TRUE, even if CMAKE_SYSTEM_NAME matches
# CMAKE_HOST_SYSTEM_NAME. To avoid potential misconfiguration of CMake,
# it is best not to touch CMAKE_SYSTEM_NAME unless cross-compiling is
# intended.
if(@depends_crosscompiling@)
  set(CMAKE_SYSTEM_NAME @host_system_name@)
  set(CMAKE_SYSTEM_VERSION @host_system_version@)
  set(CMAKE_SYSTEM_PROCESSOR @host_arch@)

  set(CMAKE_C_COMPILER_TARGET @host@)
  set(CMAKE_CXX_COMPILER_TARGET @host@)
  set(CMAKE_OBJCXX_COMPILER_TARGET @host@)
endif()

if(NOT DEFINED CMAKE_C_FLAGS_INIT)
  set(CMAKE_C_FLAGS_INIT "@CFLAGS@")
endif()
if(NOT DEFINED CMAKE_C_FLAGS_RELWITHDEBINFO_INIT)
  set(CMAKE_C_FLAGS_RELWITHDEBINFO_INIT "@CFLAGS_RELEASE@")
endif()
if(NOT DEFINED CMAKE_C_FLAGS_DEBUG_INIT)
  set(CMAKE_C_FLAGS_DEBUG_INIT "@CFLAGS_DEBUG@")
endif()

if(NOT DEFINED CMAKE_C_COMPILER)
  set(CMAKE_C_COMPILER @CC@)
endif()

if(NOT DEFINED CMAKE_CXX_FLAGS_INIT)
  set(CMAKE_CXX_FLAGS_INIT "@CXXFLAGS@")
  set(CMAKE_OBJCXX_FLAGS_INIT "@CXXFLAGS@")
endif()
if(NOT DEFINED CMAKE_CXX_FLAGS_RELWITHDEBINFO_INIT)
  set(CMAKE_CXX_FLAGS_RELWITHDEBINFO_INIT "@CXXFLAGS_RELEASE@")
  set(CMAKE_OBJCXX_FLAGS_RELWITHDEBINFO_INIT "@CXXFLAGS_RELEASE@")
endif()
if(NOT DEFINED CMAKE_CXX_FLAGS_DEBUG_INIT)
  set(CMAKE_CXX_FLAGS_DEBUG_INIT "@CXXFLAGS_DEBUG@")
  set(CMAKE_OBJCXX_FLAGS_DEBUG_INIT "@CXXFLAGS_DEBUG@")
endif()

if(NOT DEFINED CMAKE_CXX_COMPILER)
  set(CMAKE_CXX_COMPILER @CXX@)
  set(CMAKE_OBJCXX_COMPILER ${CMAKE_CXX_COMPILER})
endif()

# The DEPENDS_COMPILE_DEFINITIONS* variables are to be treated as lists.
set(DEPENDS_COMPILE_DEFINITIONS @CPPFLAGS@)
set(DEPENDS_COMPILE_DEFINITIONS_RELWITHDEBINFO @CPPFLAGS_RELEASE@)
set(DEPENDS_COMPILE_DEFINITIONS_DEBUG @CPPFLAGS_DEBUG@)

if(NOT DEFINED CMAKE_EXE_LINKER_FLAGS_INIT)
  set(CMAKE_EXE_LINKER_FLAGS_INIT "@LDFLAGS@")
endif()
if(NOT DEFINED CMAKE_SHARED_LINKER_FLAGS_INIT)
  set(CMAKE_SHARED_LINKER_FLAGS_INIT "@LDFLAGS@")
endif()
if(NOT DEFINED CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO_INIT)
  set(CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO_INIT "@LDFLAGS_RELEASE@")
endif()
if(NOT DEFINED CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO_INIT)
  set(CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO_INIT "@LDFLAGS_RELEASE@")
endif()
if(NOT DEFINED CMAKE_EXE_LINKER_FLAGS_DEBUG_INIT)
  set(CMAKE_EXE_LINKER_FLAGS_DEBUG_INIT "@LDFLAGS_DEBUG@")
endif()
if(NOT DEFINED CMAKE_SHARED_LINKER_FLAGS_DEBUG_INIT)
  set(CMAKE_SHARED_LINKER_FLAGS_DEBUG_INIT "@LDFLAGS_DEBUG@")
endif()

set(CMAKE_AR "@AR@")
set(CMAKE_RANLIB "@RANLIB@")
set(CMAKE_STRIP "@STRIP@")
set(CMAKE_OBJCOPY "@OBJCOPY@")
set(CMAKE_OBJDUMP "@OBJDUMP@")

# Using our own built dependencies should not be
# affected by a potentially random environment.
set(CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH OFF)

set(CMAKE_FIND_ROOT_PATH "${CMAKE_CURRENT_LIST_DIR}")
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)
set(QT_TRANSLATIONS_DIR "${CMAKE_CURRENT_LIST_DIR}/translations")

# The following is only necessary when using cmake from Nix or NixOS, because
# Nix patches cmake to remove the root directory `/` from
# CMAKE_SYSTEM_PREFIX_PATH. Adding it back is harmless on other platforms and
# necessary on Nix because without it cmake find_path, find_package, etc
# functions do not know where to look in CMAKE_FIND_ROOT_PATH for dependencies
# (https://github.com/bitcoin/bitcoin/issues/32428).
#
# TODO: longer term, it may be possible to use a dependency provider, which
# would bring the find_package calls completely under our control, making this
# patch unnecessary.
#
# Make sure we only append once, as this file may be called repeatedly.
if(NOT "/" IN_LIST CMAKE_PREFIX_PATH)
  list(APPEND CMAKE_PREFIX_PATH "/")
endif()

if(CMAKE_SYSTEM_NAME STREQUAL "Darwin" AND NOT CMAKE_HOST_APPLE)
  # The find_package(Qt ...) function internally uses find_library()
  # calls for all dependencies to ensure their availability.
  # In turn, the find_library() inspects the well-known locations
  # on the file system; therefore, a hint is required.
  set(CMAKE_FRAMEWORK_PATH "@OSX_SDK@/System/Library/Frameworks")
endif()


if(CMAKE_SYSTEM_NAME MATCHES "^(Linux|FreeBSD)$")
  # Customize pkg-config behavior for finding dependencies
  # of the xcb QPA platform plugin:
  # 1. Restrict search paths to the depends.
  # 2. Make output suitable for static linking.
  cmake_path(APPEND CMAKE_CURRENT_LIST_DIR "lib" "pkgconfig" OUTPUT_VARIABLE pkg_config_path)
  set(ENV{PKG_CONFIG_PATH} ${pkg_config_path})
  set(ENV{PKG_CONFIG_LIBDIR} ${pkg_config_path})
  unset(pkg_config_path)
  set(PKG_CONFIG_ARGN --static)
endif()


# Set configuration options for the main build system.
# The depends/Makefile can generate values with "not-set"
# semantics as empty strings or strings containing only spaces.
# Therefore, MATCHES must be used rather than STREQUAL.
if("@qt_packages@" MATCHES "^[ ]*$")
  set(BUILD_GUI OFF CACHE BOOL "")
else()
  set(BUILD_GUI ON CACHE BOOL "")
  set(Qt6_ROOT "${CMAKE_CURRENT_LIST_DIR}" CACHE PATH "")
endif()

if("@qrencode_packages@" MATCHES "^[ ]*$")
  set(WITH_QRENCODE OFF CACHE BOOL "")
else()
  set(WITH_QRENCODE ON CACHE BOOL "")
endif()

if("@zmq_packages@" MATCHES "^[ ]*$")
  set(WITH_ZMQ OFF CACHE BOOL "")
else()
  set(WITH_ZMQ ON CACHE BOOL "")
endif()

if("@wallet_packages@" MATCHES "^[ ]*$")
  set(ENABLE_WALLET OFF CACHE BOOL "")
else()
  set(ENABLE_WALLET ON CACHE BOOL "")
endif()

if("@usdt_packages@" MATCHES "^[ ]*$")
  set(WITH_USDT OFF CACHE BOOL "")
else()
  set(WITH_USDT ON CACHE BOOL "")
endif()

if("@multiprocess@" STREQUAL "1")
  set(ENABLE_IPC ON CACHE BOOL "")
  set(MPGEN_EXECUTABLE "${CMAKE_CURRENT_LIST_DIR}/native/bin/mpgen" CACHE FILEPATH "")
  set(CAPNP_EXECUTABLE "${CMAKE_CURRENT_LIST_DIR}/native/bin/capnp" CACHE FILEPATH "")
  set(CAPNPC_CXX_EXECUTABLE "${CMAKE_CURRENT_LIST_DIR}/native/bin/capnpc-c++" CACHE FILEPATH "")
else()
  set(ENABLE_IPC OFF CACHE BOOL "")
endif()
